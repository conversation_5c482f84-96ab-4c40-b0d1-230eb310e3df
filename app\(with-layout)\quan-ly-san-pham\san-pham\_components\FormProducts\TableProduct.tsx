import { useFormContext, useFieldArray } from 'react-hook-form';
import { IProduct, IProductOption } from '@/apis/product/product.type';
import { installationType } from '@/constants/sharedData/sharedData';
import { formatVND } from '@/constants/format';
import { Button, Col, Table } from 'reactstrap';
import { useEffect, useState } from 'react';

interface TableProductProps {
    initValue?: IProduct;
    page: string;
}

interface CurrencyInputProps {
    name: string;
    placeholder: string;
    register: any;
    value?: number;
}

const CurrencyInput = ({
    name,
    placeholder,
    register,
    value,
}: CurrencyInputProps) => {
    const [isFocused, setIsFocused] = useState(false);
    const [displayValue, setDisplayValue] = useState('');

    // Cập nhật display value khi value thay đổi và không đang focus
    useEffect(() => {
        if (!isFocused) {
            if (value && value > 0) {
                setDisplayValue(formatVND(value));
            } else {
                setDisplayValue('');
            }
        }
    }, [value, isFocused]);

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(true);
        // Khi focus, hiển thị giá trị số thuần túy
        const currentValue = value || 0;
        e.target.value = currentValue.toString();
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsFocused(false);
        const numValue = parseFloat(e.target.value) || 0;

        // Cập nhật display value với format VNĐ
        if (numValue > 0) {
            setDisplayValue(formatVND(numValue));
        } else {
            setDisplayValue('');
        }
    };

    return (
        <>
            {isFocused ? (
                <input
                    {...register(name, {
                        valueAsNumber: true,
                    })}
                    type='number'
                    placeholder={placeholder}
                    min='0'
                    step='0.01'
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    autoFocus
                    style={{
                        padding: '4px 0px 4px 0px',
                        width: '100%',
                        border: 'none',
                        background: 'transparent',
                    }}
                />
            ) : (
                <input
                    type='text'
                    value={displayValue}
                    placeholder={placeholder}
                    onFocus={handleFocus}
                    readOnly
                    style={{
                        padding: '4px 0px 4px 0px',
                        width: '100%',
                        border: 'none',
                        background: 'transparent',
                        cursor: 'pointer',
                    }}
                />
            )}
        </>
    );
};

const TableProduct = ({ initValue, page }: TableProductProps) => {
    const { control, watch, setValue, register } = useFormContext<IProduct>();
    const { fields, append, remove } = useFieldArray({
        control,
        name: 'productOptions',
    });
    useEffect(() => {
        if (
            initValue?.productOptions &&
            initValue.productOptions.length > 0 &&
            fields.length === 0
        ) {
            initValue.productOptions.forEach((option) => {
                append({
                    ...option,
                    yearsOfUse: option.yearsOfUse || 2025,
                    productOptionType: option.productOptionType || 1,
                });
            });
        }
    }, [initValue?.productOptions, append, fields.length]);

    useEffect(() => {
        fields.forEach((_, index) => {
            setValue(`productOptions.${index}.yearsOfUse`, 2025);
            setValue(`productOptions.${index}.productOptionType`, 1);
        });
    }, [fields, setValue]);

    const handleAddRow = () => {
        append({
            installationType: undefined,
            userCount: undefined,
            basePrice: undefined,
            yearsOfUse: 2025,
            productOptionType: 1,
        });
    };

    const handleRemoveRow = (index: number) => {
        remove(index);
    };

    const watchedValues = watch('productOptions');

    const getInstallationTypeLabel = (value: number) => {
        const option = installationType.find(
            (item) => item.value === value.toString(),
        );
        return option ? option.label : '';
    };

    return (
        <Col xs={12}>
            <div>
                {page !== 'chi-tiet' && (
                    <div
                        style={{
                            marginTop: '16px',
                            display: 'flex',
                            justifyContent: 'flex-end',
                        }}
                    >
                        <Button
                            onClick={handleAddRow}
                            style={{
                                backgroundColor: '#ffffff',
                                borderColor: '#0ab39c',
                                color: '#0ab39c',
                            }}
                        >
                            + Thêm tùy chọn sản phẩm
                        </Button>
                    </div>
                )}

                <Table style={{ marginTop: '20px' }}>
                    <thead
                        style={{
                            backgroundColor: '#f3f6f9',
                        }}
                    >
                        <tr>
                            <th style={{ width: '5%' }}>STT</th>
                            <th style={{ width: '30%' }}>Kiểu cài đặt</th>
                            <th style={{ width: '30%' }}>Số lượng user</th>
                            <th style={{ width: '25%' }}>Giá gốc (VNĐ)</th>
                            {page !== 'chi-tiet' && (
                                <th style={{ width: '10%' }}>Thao tác</th>
                            )}
                        </tr>
                    </thead>
                    <tbody>
                        {fields.length === 0 && (
                            <tr>
                                <td
                                    colSpan={5}
                                    style={{
                                        textAlign: 'center',
                                        padding: '20px',
                                        color: '#6c757d',
                                    }}
                                >
                                    Chưa có thông tin
                                </td>
                            </tr>
                        )}

                        {fields.map((field, index) => {
                            const currentValue = watchedValues?.[index];
                            const installationTypeValue =
                                currentValue?.installationType ||
                                field.installationType ||
                                1;
                            const userCountValue =
                                currentValue?.userCount || field.userCount;
                            const basePriceValue =
                                currentValue?.basePrice || field.basePrice;

                            return (
                                <tr key={field.id}>
                                    <td>{index + 1}</td>
                                    <td>
                                        {page === 'chi-tiet' ? (
                                            getInstallationTypeLabel(
                                                installationTypeValue,
                                            )
                                        ) : (
                                            <select
                                                {...register(
                                                    `productOptions.${index}.installationType`,
                                                )}
                                                style={{
                                                    padding: '4px 0px 4px 0px',
                                                    width: '30%',
                                                    border: 'none',
                                                    background: 'transparent',
                                                }}
                                            >
                                                {installationType.map(
                                                    (option) => (
                                                        <option
                                                            key={option.value}
                                                            value={option.value}
                                                        >
                                                            {option.label}
                                                        </option>
                                                    ),
                                                )}
                                            </select>
                                        )}
                                    </td>
                                    <td>
                                        {page === 'chi-tiet' ? (
                                            userCountValue
                                        ) : (
                                            <input
                                                type='number'
                                                {...register(
                                                    `productOptions.${index}.userCount`,
                                                    {
                                                        valueAsNumber: true,
                                                    },
                                                )}
                                                placeholder='Nhập số lượng user'
                                                min='0'
                                                style={{
                                                    padding: '4px 0px 4px 0px',
                                                    width: '100%',
                                                    border: 'none',
                                                }}
                                            />
                                        )}
                                    </td>
                                    <td>
                                        {page === 'chi-tiet' ? (
                                            formatVND(basePriceValue)
                                        ) : (
                                            <CurrencyInput
                                                name={`productOptions.${index}.basePrice`}
                                                placeholder='Nhập giá gốc'
                                                register={register}
                                                value={basePriceValue}
                                            />
                                        )}
                                    </td>
                                    {page !== 'chi-tiet' && (
                                        <td>
                                            <button
                                                type='button'
                                                onClick={() =>
                                                    handleRemoveRow(index)
                                                }
                                                title='Xóa'
                                                style={{
                                                    color: '#e74c3c',
                                                    fontSize: '20px',
                                                    cursor: 'pointer',
                                                }}
                                            >
                                                <i className='ri-delete-bin-line'></i>
                                            </button>
                                        </td>
                                    )}
                                </tr>
                            );
                        })}
                    </tbody>
                </Table>
            </div>
        </Col>
    );
};
export default TableProduct;
