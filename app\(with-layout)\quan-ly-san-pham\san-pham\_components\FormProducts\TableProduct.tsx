import { useFormContext, useFieldArray } from 'react-hook-form';
import { IProduct, IProductOption } from '@/apis/product/product.type';
import { installationType } from '@/constants/sharedData/sharedData';
import { formatUSD } from '@/constants/format';
import { Button, Col, Table } from 'reactstrap';
import { useEffect } from 'react';

interface TableProductProps {
    initValue?: IProduct;
    page: string;
}

const TableProduct = ({ initValue, page }: TableProductProps) => {
    const { control, watch, setValue, register } = useFormContext<IProduct>();
    const { fields, append, remove } = useFieldArray({
        control,
        name: 'productOptions',
    });
    useEffect(() => {
        if (
            initValue?.productOptions &&
            initValue.productOptions.length > 0 &&
            fields.length === 0
        ) {
            initValue.productOptions.forEach((option) => {
                append({
                    ...option,
                    yearsOfUse: option.yearsOfUse || 2025,
                    productOptionType: option.productOptionType || 1,
                });
            });
        }
    }, [initValue?.productOptions, append, fields.length]);

    useEffect(() => {
        fields.forEach((_, index) => {
            setValue(`productOptions.${index}.yearsOfUse`, 2025);
            setValue(`productOptions.${index}.productOptionType`, 1);
        });
    }, [fields, setValue]);

    const handleAddRow = () => {
        append({
            installationType: undefined,
            userCount: undefined,
            basePrice: undefined,
            yearsOfUse: 2025,
            productOptionType: 1,
        });
    };

    const handleRemoveRow = (index: number) => {
        remove(index);
    };

    const watchedValues = watch('productOptions');

    const getInstallationTypeLabel = (value: number) => {
        const option = installationType.find(
            (item) => item.value === value.toString(),
        );
        return option ? option.label : '';
    };

    return (
        <Col xs={12}>
            <div>
                {page !== 'chi-tiet' && (
                    <div
                        style={{
                            marginTop: '16px',
                            display: 'flex',
                            justifyContent: 'flex-end',
                        }}
                    >
                        <Button
                            onClick={handleAddRow}
                            style={{
                                backgroundColor: '#ffffff',
                                borderColor: '#0ab39c',
                                color: '#0ab39c',
                            }}
                        >
                            + Thêm tùy chọn sản phẩm
                        </Button>
                    </div>
                )}

                <Table style={{ marginTop: '20px' }}>
                    <thead
                        style={{
                            backgroundColor: '#f3f6f9',
                        }}
                    >
                        <tr>
                            <th style={{ width: '5%' }}>STT</th>
                            <th style={{ width: '30%' }}>Kiểu cài đặt</th>
                            <th style={{ width: '30%' }}>Số lượng user</th>
                            <th style={{ width: '25%' }}>Giá gốc</th>
                            {page !== 'chi-tiet' && (
                                <th style={{ width: '10%' }}>Thao tác</th>
                            )}
                        </tr>
                    </thead>
                    <tbody>
                        {fields.length === 0 && (
                            <tr>
                                <td
                                    colSpan={5}
                                    style={{
                                        textAlign: 'center',
                                        padding: '20px',
                                        color: '#6c757d',
                                    }}
                                >
                                    Chưa có thông tin
                                </td>
                            </tr>
                        )}

                        {fields.map((field, index) => {
                            const currentValue = watchedValues?.[index];
                            const installationTypeValue =
                                currentValue?.installationType ||
                                field.installationType ||
                                1;
                            const userCountValue =
                                currentValue?.userCount || field.userCount || 0;
                            const basePriceValue =
                                currentValue?.basePrice || field.basePrice || 0;

                            return (
                                <tr key={field.id}>
                                    <td>{index + 1}</td>
                                    <td>
                                        {page === 'chi-tiet' ? (
                                            getInstallationTypeLabel(
                                                installationTypeValue,
                                            )
                                        ) : (
                                            <select
                                                {...register(
                                                    `productOptions.${index}.installationType`,
                                                )}
                                                style={{
                                                    padding: '4px 0px 4px 0px',
                                                    width: '30%',
                                                    border: 'none',
                                                    background: 'transparent',
                                                }}
                                            >
                                                {installationType.map(
                                                    (option) => (
                                                        <option
                                                            key={option.value}
                                                            value={option.value}
                                                        >
                                                            {option.label}
                                                        </option>
                                                    ),
                                                )}
                                            </select>
                                        )}
                                    </td>
                                    <td>
                                        {page === 'chi-tiet' ? (
                                            userCountValue
                                        ) : (
                                            <input
                                                type='number'
                                                {...register(
                                                    `productOptions.${index}.userCount`,
                                                    {
                                                        valueAsNumber: true,
                                                    },
                                                )}
                                                placeholder='Nhập số lượng user'
                                                min='0'
                                                style={{
                                                    padding: '4px 0px 4px 0px',
                                                    width: '100%',
                                                    border: 'none',
                                                }}
                                            />
                                        )}
                                    </td>
                                    <td>
                                        {page === 'chi-tiet' ? (
                                            formatUSD(basePriceValue)
                                        ) : (
                                            <input
                                                type='number'
                                                {...register(
                                                    `productOptions.${index}.basePrice`,
                                                    {
                                                        valueAsNumber: true,
                                                    },
                                                )}
                                                placeholder='Nhập giá gốc (USD)'
                                                min='0'
                                                step='0.01'
                                                style={{
                                                    padding: '4px 0px 4px 0px',
                                                    width: '100%',
                                                    border: 'none',
                                                    background: 'transparent',
                                                }}
                                            />
                                        )}
                                    </td>
                                    {page !== 'chi-tiet' && (
                                        <td>
                                            <button
                                                type='button'
                                                onClick={() =>
                                                    handleRemoveRow(index)
                                                }
                                                title='Xóa'
                                                style={{
                                                    color: '#e74c3c',
                                                    fontSize: '20px',
                                                    cursor: 'pointer',
                                                }}
                                            >
                                                <i className='ri-delete-bin-line'></i>
                                            </button>
                                        </td>
                                    )}
                                </tr>
                            );
                        })}
                    </tbody>
                </Table>
            </div>
        </Col>
    );
};
export default TableProduct;
